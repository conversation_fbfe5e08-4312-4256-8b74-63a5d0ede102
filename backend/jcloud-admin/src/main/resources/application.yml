# jCloud管理后台配置文件
server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: jcloud-admin
  profiles:
    active: prod
  # 禁用Spring Security自动配置（我们使用sa-token）
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      master:
        #        url: **************************************************************************************************************************************************************
        url: **************************************************************************************************************************************************************
        #        username: voltskins
        username: vimbox
        password: Vimbox123@
        #        password: ShbAeEVw7RNh8arDzjN4eZhsh@
        # 主库连接池配置 - 性能优化
        initial-size: 20
        min-idle: 15
        max-active: 50
        max-wait: 5000
        # 优化连接管理，减少验证开销
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 120000
        validation-query: SELECT 1
        validation-query-timeout: 1
        test-while-idle: false
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 50
        filters: stat,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
        # 生产环境稳定性配置
        keep-alive: true
        keep-alive-between-time-millis: 120000
        phy-timeout-millis: 300000
        # 连接泄露检测优化
        remove-abandoned: true
        remove-abandoned-timeout: 300
        log-abandoned: true
      slave:
        url: ***************************************************************************************************************************************************
        username: vimbox
        password: Vimbox123@
        # 从库连接池配置 - 开发环境
        initial-size: 10
        min-idle: 5
        max-active: 50
        max-wait: 5000
        # 连接生命周期管理
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 600000
        # 连接验证配置 - 开发环境简化
        validation-query: SELECT 1
        validation-query-timeout: 2
        test-while-idle: true
        test-on-borrow: false        # 开发环境关闭借用验证
        test-on-return: false
        # 连接池配置
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 30
        filters: stat,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
        # MySQL保活机制
        keep-alive: true
        keep-alive-between-time-millis: 120000
        phy-timeout-millis: 300000
        # 连接泄露检测
        remove-abandoned: true
        remove-abandoned-timeout: 300
        log-abandoned: true
      # 连接泄漏检测和清理 - 生产环境适中检测
      removeAbandoned: false       # 生产环境关闭连接泄漏检测（避免误杀长事务）
      removeAbandonedTimeout: 180  # 连接泄漏超时时间（3分钟）
      logAbandoned: true          # 记录连接泄漏日志

      # ==================== 监控和统计配置 ====================
      webStatFilter:
        enabled: true
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      statViewServlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: druid123
        allow: 127.0.0.1,***********/24
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500      # 死锁优化：降低慢SQL阈值（原来1000 -> 500ms）
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true
  # Redis配置 - 性能优化
  data:
    redis:
      host: **************
      port: 6379
      password: DAANFtJj3n5PtbM8zDkPwh5PG
      database: 10
      timeout: 10s
      connect-timeout: 5s
      lettuce:
        pool:
          max-active: 20
          max-wait: 5000ms
          max-idle: 10
          min-idle: 5
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30s



# MyBatis-Flex配置 - 生产环境优化
mybatis-flex:
  type-aliases-package: com.jcloud.**.entity
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 开发环境超时配置（秒）
    default-statement-timeout: 20
    default-fetch-size: 100
    # 开启二级缓存
    cache-enabled: true
    # 开发环境性能优化配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple    # 修改为SIMPLE，避免连接复用问题
    # 本地缓存作用域
    local-cache-scope: statement      # 修改为STATEMENT，避免连接状态混乱
    # JDBC类型为空时的处理
    jdbc-type-for-null: other
    # 延迟加载触发方法
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    # 生产环境连接管理
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    shrink-whitespaces-in-sql: true

# sa-token配置
sa-token:
  # token名称（同时也是cookie名称）
  token-name: Authorization
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  is-read-body: false
  token-prefix: Bearer

# jCloud配置
jcloud:
  # 验证码配置
  captcha:
    # 是否启用验证码
    enabled: true
    # 验证码类型：NUMERIC(数字)、ALPHABETIC(字母)、ALPHANUMERIC(数字字母混合)、ARITHMETIC(算术)
    type: ALPHANUMERIC
    # 验证码长度
    length: 4
    # 验证码有效期（秒）
    expire-time: 300
    # 是否区分大小写
    case-sensitive: false
    # 图片配置
    image:
      # 图片宽度
      width: 120
      # 图片高度
      height: 40
      # 字体名称
      font-name: Arial
      # 字体大小
      font-size: 25
      # 干扰线数量
      line-count: 5
      # 噪点数量
      noise-count: 50
      # 背景颜色（RGB格式）
      background-color: "255,255,255"
      # 文字颜色范围（RGB格式）
      text-color-range: "0,0,0-100,100,100"
      # 干扰线颜色范围（RGB格式）
      line-color-range: "150,150,150-200,200,200"
      # 噪点颜色范围（RGB格式）
      noise-color-range: "100,100,100-150,150,150"
    # 安全配置
    security:
      # 验证码验证失败最大次数
      max-verify-attempts: 5
      # 验证码验证失败锁定时间（秒）
      lock-time: 600
      # 是否启用防暴力破解
      enable-brute-force-protection: true
  # 缓存配置
  cache:
    # 启用缓存
    enabled: true
    # 缓存类型：redis, caffeine, composite
    type: redis
    # Redis缓存配置
    redis:
      # 缓存过期时间（秒）
      time-to-live: 3600
      # 是否缓存空值
      cache-null-values: false
      # 键前缀
      key-prefix: "jcloud:cache:"
      # 是否使用键前缀
      use-key-prefix: true
    # Caffeine本地缓存配置
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s
    # 缓存名称配置
    cache-names:
      - userCache
      - roleCache
      - permissionCache
      - tenantCache
      - menuCache
      - deptCache
# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: jCloud权限管理系统API文档
    description: 企业级权限管理系统接口文档
    version: 1.0.0
    concat: <EMAIL>
    license: MIT
    license-url: https://opensource.org/licenses/MIT
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

    # SQL审计配置
    # 是否启用SQL审计功能
    enabled: true
    # 慢SQL阈值（毫秒）
    slow-sql-threshold: 1000
    # 存储方式：database, file, console
    storage-types:
      - database
      - console
    # 数据脱敏配置
    data-masking:
      # 是否启用数据脱敏
      enabled: true
      # 需要脱敏的字段名（支持正则表达式）
      sensitive-fields:
        - password
        - pwd
        - passwd
        - phone
        - mobile
        - email
        - idcard
        - identity
        - bankcard
        - credit_card
    # 数据库存储配置
    database:
      # 是否异步存储
      async: true
      # 批量大小
      batch-size: 100
      # 刷新间隔（秒）
      flush-interval: 30
      # 保留天数（0表示永久保留）
      retention-days: 90
    # 文件存储配置
    file:
      # 日志文件路径
      path: ./logs/sql-audit
      # 文件名模式
      filename-pattern: sql-audit-%d{yyyy-MM-dd}.log
      # 单个文件最大大小
      max-file-size: 100MB
      # 最大历史文件数
      max-history: 30
    # 性能配置
    performance:
      # 是否启用性能监控
      monitor-enabled: true
      # 缓冲区大小
      buffer-size: 1000
      # 最大等待时间（毫秒）
      max-wait-time: 5000

# 日志配置 - 生产环境
logging:
  level:
    com.jcloud: info
    org.springframework.security: warn
    # 数据库连接相关日志
    com.alibaba.druid: info
    com.alibaba.druid.pool: info
    com.alibaba.druid.pool.DruidDataSource: info
    # SQL审计日志级别
    mybatis-flex-sql: info
    # 连接池监控日志
    druid.sql: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: ./logs/jcloud-admin.log
    max-size: 100MB
    max-history: 30

# 管理端点配置 - 生产环境监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,druid
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
      show-components: when_authorized
    druid:
      enabled: true
  health:
    db:
      enabled: true
    datasource:
      enabled: true
