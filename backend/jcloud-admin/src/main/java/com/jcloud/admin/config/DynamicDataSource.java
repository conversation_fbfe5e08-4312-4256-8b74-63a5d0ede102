package com.jcloud.admin.config;

import com.jcloud.common.config.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 动态数据源路由器
 * 根据当前线程上下文中的数据源标识来路由到对应的数据源
 * 增强连接管理和异常处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {

    /**
     * 确定当前查找键
     * 该方法返回的值将作为键来查找目标数据源
     *
     * @return 数据源标识
     */
    @Override
    protected Object determineCurrentLookupKey() {
        String dataSource = DataSourceContextHolder.getDataSource();
        log.debug("当前使用数据源: {}", dataSource);
        return dataSource;
    }

    /**
     * 重写getConnection方法，增加连接获取的重试机制和异常处理
     */
    @Override
    public Connection getConnection() throws SQLException {
        String currentDataSource = DataSourceContextHolder.getDataSource();
        int retryCount = 0;
        int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                Connection connection = super.getConnection();

                // 验证连接有效性
                if (connection != null && connection.isValid(5)) {
                    log.debug("成功获取数据源 {} 的连接", currentDataSource);
                    return connection;
                } else {
                    log.warn("获取到无效连接，数据源: {}, 重试次数: {}", currentDataSource, retryCount + 1);
                    if (connection != null) {
                        connection.close();
                    }
                }
            } catch (SQLException e) {
                retryCount++;
                log.warn("获取数据源 {} 连接失败，重试次数: {}, 错误: {}",
                        currentDataSource, retryCount, e.getMessage());

                if (retryCount >= maxRetries) {
                    log.error("获取数据源 {} 连接失败，已达到最大重试次数", currentDataSource);
                    throw new SQLException("无法获取数据源连接: " + currentDataSource, e);
                }

                // 短暂等待后重试
                try {
                    Thread.sleep(100 * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SQLException("连接获取被中断", ie);
                }
            }
            retryCount++;
        }

        throw new SQLException("无法获取有效的数据库连接: " + currentDataSource);
    }
}