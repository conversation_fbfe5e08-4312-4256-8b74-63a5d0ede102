@echo off
echo ========================================
echo jCloud项目重新编译脚本
echo 解决MyBatis OGNL和Lombok Builder问题
echo ========================================

echo.
echo 1. 清理项目...
call mvn clean

echo.
echo 2. 编译公共模块...
cd jcloud-common
call mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo 公共模块编译失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 3. 编译认证模块...
cd jcloud-auth
call mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo 认证模块编译失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 4. 编译管理模块...
cd jcloud-admin
call mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo 管理模块编译失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 5. 打包整个项目...
call mvn clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo 项目打包失败！
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo 项目重新编译完成！
    echo 已解决MyBatis OGNL和Lombok Builder问题
    echo ========================================
)

pause
